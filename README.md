# AI 阅卷反馈系统

基于 Cloudflare Workers + Pages + Supabase 构建的 AI 阅卷效果评测系统。

## 项目概览

这是一个安全、高效、移动优先的网页应用，供已认证用户评测和对比不同 AI 模型的阅卷结果，并将反馈数据用于模型和提示词的迭代。

### 技术栈

- **前端**: Cloudflare Pages (HTML, CSS, Vanilla JavaScript)
- **后端**: Cloudflare Workers (TypeScript)
- **数据库**: Cloudflare D1
- **文件存储**: Cloudflare R2
- **用户认证**: Supabase Auth

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 平台设置

#### Cloudflare 设置

1. **创建 R2 存储桶**:
   - 进入 Cloudflare Dashboard -> R2
   - 创建存储桶 `ai-feedback-results`
   - 在存储桶设置中启用公开访问
   - 记录公开访问域名 (如: `https://pub-xxxx.r2.dev`)
   - 上传 `results.json` 文件和学生答题图片

2. **创建 D1 数据库**:
   - 进入 Cloudflare Dashboard -> Workers & Pages -> D1
   - 创建数据库 `ai-feedback-db`
   - 在数据库控制台执行以下 SQL:

```sql
CREATE TABLE Feedbacks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  case_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  user_email TEXT,
  best_solution TEXT NOT NULL,
  problems TEXT,
  comment TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_case_user ON Feedbacks (case_id, user_id);
```

#### Supabase 设置

1. **创建 Supabase 项目**
2. **获取配置信息**:
   - Project Settings -> API: 复制 Project URL 和 anon public key
   - Project Settings -> API -> JWT Settings: 复制 JWT Secret
3. **配置认证**:
   - Authentication -> Providers: 启用 Email 认证
   - 关闭邮件确认以简化测试

### 3. 配置环境变量

#### 本地开发

编辑 `.dev.vars` 文件:

```env
SUPABASE_JWT_SECRET="your_actual_jwt_secret"
R2_PUBLIC_URL="https://pub-xxxx.r2.dev"
```

#### 前端配置

编辑 `public/script.js` 中的 Supabase 配置:

```javascript
const SUPABASE_URL = "https://your-project-ref.supabase.co";
const SUPABASE_ANON_KEY = "your_anon_public_key";
```

#### wrangler.jsonc 配置

更新 `wrangler.jsonc` 中的配置:

```json
{
  "d1_databases": [{
    "binding": "DB",
    "database_name": "ai-feedback-db",
    "database_id": "your_actual_database_id"
  }],
  "r2_buckets": [{
    "binding": "RESULTS_BUCKET",
    "bucket_name": "ai-feedback-results"
  }],
  "vars": {
    "SUPABASE_JWT_SECRET": "your_actual_jwt_secret",
    "R2_PUBLIC_URL": "https://pub-xxxx.r2.dev"
  }
}
```

### 4. 本地开发

```bash
# 启动开发服务器
npm start

# 或者分别启动 Worker 和 Pages
npm run dev  # Worker 开发服务器
```

访问 `http://localhost:8788` 查看应用。

### 5. 部署

```bash
# 部署 Worker
npm run deploy-worker

# 部署 Pages
npm run deploy-pages
```

**重要**: 部署后需要在 Cloudflare Dashboard 中配置生产环境变量:
- Workers & Pages -> 你的 Worker -> Settings -> Variables
- 添加 `SUPABASE_JWT_SECRET` 和 `R2_PUBLIC_URL`

## 项目结构

```
ai-feedback-system/
├── public/                  # 前端代码 (Cloudflare Pages)
│   ├── index.html          # 主页面
│   ├── style.css           # 样式文件
│   └── script.js           # 前端逻辑
├── src/                    # 后端代码 (Cloudflare Workers)
│   └── index.ts            # Worker 主文件
├── .dev.vars              # 本地环境变量
├── package.json           # 项目配置
├── wrangler.jsonc         # Cloudflare 配置
└── README.md              # 项目说明
```

## 功能特性

- ✅ **用户认证**: 基于 Supabase Auth 的安全登录
- ✅ **案例展示**: 显示学生答题图片和评分标准
- ✅ **AI 结果对比**: 展示多个 AI 模型的阅卷结果
- ✅ **反馈收集**: 用户可以评选最佳方案并指出问题
- ✅ **进度跟踪**: 自动跳过已评测的案例
- ✅ **响应式设计**: 移动端友好的界面
- ✅ **数据持久化**: 反馈数据存储在 D1 数据库

## API 接口

### GET /api/get-case

获取下一个待评测案例。

**响应示例**:

```json
{
  "case_id": "case_001",
  "image_url": "https://pub-xxxx.r2.dev/student_answer_001.jpg",
  "criteria": "评分标准...",
  "ai_results": [
    {
      "id": "model_a",
      "name": "模型 A",
      "ocr_text": "识别的文本...",
      "score": "85",
      "details": "评分详情..."
    }
  ]
}
```

### POST /api/submit-feedback

提交用户反馈。

**请求体**:

```json
{
  "case_id": "case_001",
  "best_solution": "model_a",
  "problems": ["识别/OCR错误", "打分过松"],
  "comment": "其他意见..."
}
```

## 数据格式

### promptfoo results.json 格式

系统期望的 `results.json` 格式:

```json
{
  "results": {
    "tests": [
      {
        "vars": {
          "case_id": "case_001",
          "image_file": "student_answer_001.jpg",
          "criteria": "评分标准..."
        },
        "prompts": [
          {
            "provider": "model_a",
            "label": "模型 A"
          }
        ],
        "outputs": [
          {
            "vars": {
              "ocr_text": "识别的文本...",
              "score": "85"
            },
            "value": "评分详情..."
          }
        ]
      }
    ]
  }
}
```

## 故障排除

### 常见问题

1. **登录失败**
   - 检查 Supabase 配置是否正确
   - 确认用户已在 Supabase 中注册

2. **无法加载案例**
   - 检查 R2 存储桶中是否有 `results.json`
   - 确认 R2 公开访问配置正确

3. **提交反馈失败**
   - 检查 D1 数据库表是否创建
   - 确认 JWT Secret 配置正确

### 开发调试

```bash
# 查看 Worker 日志
wrangler tail

# 生成类型定义
npm run cf-typegen

# 运行测试
npm test
```

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License

通过建立一个人工反馈闭环，可以持续、高效地优化 AI 的性能。这套系统不仅能改进 AI 阅卷，其思路也可以应用在任何需要"人工审核-反馈-迭代"的 AI 应用中。

下面我将为您详细规划这个网页系统的设计方案，包括 **页面设计、功能模块、技术架构、以及如何利用收集到的数据来改进提示词**。

---

## 一、项目目标与核心流程

**目标：** 创建一个内部评估平台，让授权用户（如教师、教研员）能够高效地审查 AI 的阅卷结果，并提供结构化的反馈，最终利用这些反馈数据来迭代和优化 AI 的评分模型（特别是其背后的提示词）。

**核心流程：**

1. **数据导入：** AI 阅卷系统将每一份作业的（题目、学生答案、评分标准、AI 评分、AI 解释）存入数据库。
2. **用户登录与任务分配：** 评估人员登录系统，系统会展示一条待评估的阅卷记录。
3. **评估与反馈：** 评估人员在网页上查看所有信息，并填写反馈表单。
4. **数据收集：** 系统记录下评估人员的反馈，并将其与原始阅卷记录关联。
5. **分析与迭代：** 系统管理员或 AI 工程师分析汇总的反馈数据，识别 AI 的常见问题，并据此调整系统提示词。

---

## 二、网页设计 (UI/UX)

设计核心：单栏滚动，信息卡片化，突出对比。用户在一个页面内从上到下完成所有操作。

### 页面布局(单页滚动式)

```text
+----------------------------------+
|      AI 阅卷效果评测 (v1.0)      |  <-- 页面标题
+----------------------------------+
|                                  |
| ▼ 评测原文 (点击展开/折叠)       |  <-- 可折叠的区域，节省空间
+----------------------------------+
|                                  |
|   -- 方案一：通义千问VLM --       |  <-- AI方案1 卡片
|   识别文本：[OCR识别出的文字...]    |
|   AI 评分：[ 8 / 10 分 ]         |
|   评分详情：[AI给出的理由...]       |
|                                  |
+----------------------------------+
|                                  |
|   -- 方案二：文心4.0 + Kimi --    |  <-- AI方案2 卡片
|   识别文本：[OCR识别出的文字...]    |
|   AI 评分：[ 6 / 10 分 ]         |
|   评分详情：[AI给出的理由...]       |
|                                  |
+----------------------------------+
|         ... (更多方案) ...       |
+----------------------------------+
|                                  |
|   -- 请您评判 --                 |  <-- 人工反馈表单
|   1. 哪个方案的结果最好？        |
|   ( ) 方案一                     |
|   ( ) 方案二                     |
|   ( ) 都不好                     |
|                                  |
|   2. 主要问题是？(可多选)        |
|   [ ] 识别/OCR错误              |
|   [ ] 打分过松/过严              |
|   ...                            |
|                                  |
|   3. 其他意见或修改建议          |
|   [__________________________]   |
|                                  |
|   [  提 交 反 馈  ]             |
|   [    下 一 题    ]             |
|                                  |
+----------------------------------+
```

#### 关键 UI 组件详解

1. **评测原文区 (Context Area)**

   - **学生答题图片：** 默认显示缩略图，**点击可全屏查看大图**。这是移动端最重要的交互。
   - **评分标准：** 清晰的文本列表。
   - **可折叠设计：** 默认可以展开，但用户一旦熟悉了题目，可以将其折叠，专注于对比下方的 AI 结果。

2. **AI 结果卡片 (AI Result Card)**

   - **清晰的标题：** 明确告知用户这是哪个模型或工作流的产出，例如 “方案一：通义千问 VLM (多模态直出)” 或 “方案二：百度 OCR + Kimi (分步)”。
   - **突出关键信息：** **AI 评分** 用大号字体和醒目颜色展示。
   - **展示 OCR 结果：** 对于分步工作流，必须展示 OCR 识别出的文本，因为很多时候问题出在第一步。

3. **人工反馈表单 (Feedback Form)**
   - **核心选择题：** “哪个方案最好？” 这是最重要的决策数据。使用单选框。
   - **问题归因（多选）：** 简化原设计，聚焦于对比评测中常见的问题。
     - `[ ] 识别/OCR错误` (新加，非常关键)
     - `[ ] 评分标准理解错误`
     - `[ ] 事实性知识错误`
     - `[ ] 打分过松`
     - `[ ] 打分过严`
     - `[ ] 遗漏要点`
     - `[ ] 理由与分数不符`
     - `[ ] 理由不充分/牵强`
   - **开放建议：** 一个简单的文本框，用于收集额外信息。
   - **操作按钮：** “提交反馈” 和 “下一题” 按钮要大，易于点击。

---

## 三、反馈表单的详细设计 (这是系统的核心)

这部分的设计直接决定了你收集到的数据质量。你需要的是**结构化、可量化**的反馈。

### 1. 评分认可度 (Score Agreement) - `单选`

- ( ) 完全同意 (AI 分数准确无误)
- ( ) 基本同意 (AI 分数在合理误差内)
- ( ) 部分同意 (AI 分数有明显偏差)
- ( ) 完全不同意 (AI 分数严重错误)

### 2. 人工建议得分 (Suggested Score) - `数字输入框`

- 这个输入框在用户选择“部分同意”或“完全不同意”时激活。
- `您认为的正确得分：[__] 分`

### 3. 解释质量评估 (Explanation Quality) - `单选`

- ( ) 解释合理，准确引用了评分标准。
- ( ) 解释基本合理，但不够精确或全面。
- ( ) 解释牵强，与评分标准或学生答案关联不大。
- ( ) 解释错误，存在事实或逻辑问题。
- ( ) 解释与分数不符 (例如，解释说答得很好，但扣了很多分)。

### 4. AI 问题归类 (Error Categorization) - `多选`

- 这是**最关键**的部分，用于定位 Prompt 的问题。
- `[ ] 对评分标准理解有误` (例如，曲解了某个得分点的含义)
- `[ ] 事实性错误` (例如，AI 在解释中引用的知识点是错的)
- `[ ] 逻辑推理能力不足` (例如，未能理解学生答案的深层逻辑)
- `[ ] 过于严苛 / "吹毛求疵"`
- `[ ] 过于宽松 / "放水"`
- `[ ] 遗漏要点` (未能识别出学生答案中的某个得分点或失分点)
- `[ ] 无中生有` (在学生答案中“看到”了不存在的内容)
- `[ ] 其他`

### 5. 开放式建议 (Open-ended Feedback) - `文本域`

- `请详细说明您的理由，或者您会如何修改AI的解释？`
- 这个区域用于收集无法被结构化选项覆盖的、更细致的反馈。

### 6. 操作按钮

- `[提交并处理下一条]`
- `[跳过此条]` (用于处理有歧义或无法判断的样本)

---

## 四、Cloudflare 全家桶技术架构

### 1.技术栈

- 前端: Cloudflare Pages (HTML, CSS, Vanilla JavaScript)
- 后端: Cloudflare Workers
- 数据库: Cloudflare D1
- 文件存储: Cloudflare R2
- 用户认证: Supabase Auth

---

## 五、如何利用反馈数据改进系统提示词 (The Final Step)

这是整个项目的最终目的。

### 1. 数据分析与洞察

- **定量分析：**

  - **宏观层面：** 计算 AI 评分与人工评分的**平均绝对误差 (MAE)** 和**一致性比率**（选择“完全同意”和“基本同意”的比例）。这能衡量 AI 的整体性能。
  - **问题定位：** 统计 **“AI 问题归类”** 中各个选项出现的频率。
    - **如果“过于宽松”是最高频的问题**，说明你的 Prompt 需要变得更严格。
    - **如果“对评分标准理解有误”占比很高**，说明你的 Prompt 需要更明确地指导 AI 如何解读和应用评分标准。

- **定性分析：**
  - 筛选出**“完全不同意”**的案例，并仔细阅读评估人员填写的**“开放式建议”**。
  - 寻找模式：例如，评估人员是否反复提到“AI 没有注意到学生答案中的这个隐含条件”？或者“AI 对 XX 概念的理解是错误的”？

### 2. 提示词 (Prompt) 迭代策略

根据上述分析，你可以进行针对性的 Prompt 优化。

#### 示例 1：解决“过于宽松”的问题

- **原始 Prompt 可能像这样：**
  > "请根据以下评分标准，为学生的答案打分，并给出解释。"
- **分析发现：** AI 总是倾向于给高分，忽略小错误。
- **优化后的 Prompt：**
  > "你是一位**严格的、注重细节的**中学教师。请根据以下评分标准，为学生的答案打分。**请仔细检查答案中的每一个细节，任何与评分标准不符的地方都应作为扣分依据。** 给出你的分数和详细解释。"

#### 示例 2：解决“对评分标准理解有误”的问题

- **原始 Prompt 可能像这样：**
  > "评分标准：1. 定义准确(2 分)；2. 举例恰当(3 分)。"
- **分析发现：** AI 对于“定义准确”的判断很模糊，学生只是部分说对，AI 也给了满分。
- **优化后的 Prompt (使用 Few-shot 或思维链 CoT)：**
  > "你将根据评分标准为学生答案打分。在评估前，请先**分步思考**：
  >
  > 1. **解读评分标准'定义准确'**: '定义准确'意味着必须完整、无误地复述核心概念，缺少关键词或概念混淆都不能得满分。例如，'浮力是物体在水中受到的向上的力' 就不是一个完全准确的定义，因为它遗漏了'排开的液体'和'重力'的对比。
  > 2. **对照学生答案**: 学生答案是 '...'。它是否满足了上述对'准确'的严格要求？
  > 3. **给出分数和理由**: ...
  >    现在，请根据以下评分标准和学生答案，完成阅卷..."

**总结：**

您构想的这个系统是一个强大的 AI 能力增强引擎。关键在于**将模糊的人类感觉转化为机器可读、可统计的结构化数据**，然后通过数据分析找到优化方向，最终通过精巧的提示词工程（Prompt Engineering）实现 AI 能力的迭代提升。祝您项目顺利！
