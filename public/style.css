body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  background-color: #f4f4f9;
  color: #333;
  line-height: 1.6;
}

header {
  background: #fff;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

main {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.case-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.case-card h3 {
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.case-card h4 {
  color: #34495e;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

#auth-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

#login-form {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

#login-form input {
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 150px;
}

#login-form input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

button {
  padding: 0.6rem 1.2rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #2980b9;
}

button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

#logout-btn {
  background-color: #e74c3c;
}

#logout-btn:hover {
  background-color: #c0392b;
}

#submit-btn {
  background-color: #27ae60;
  font-size: 1rem;
  padding: 0.8rem 1.5rem;
  margin-top: 1rem;
}

#submit-btn:hover {
  background-color: #229954;
}

img.zoomable {
  max-width: 100%;
  height: auto;
  cursor: zoom-in;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

img.zoomable:hover {
  transform: scale(1.02);
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #3498db;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
}

#feedback-form-card p {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.8rem;
  margin-top: 1.5rem;
}

#feedback-form-card p:first-of-type {
  margin-top: 0;
}

#best-solution-options,
#problem-checkboxes {
  margin-bottom: 1rem;
}

#best-solution-options label,
#problem-checkboxes label {
  display: block;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#best-solution-options label:hover,
#problem-checkboxes label:hover {
  background: #e9ecef;
}

#best-solution-options input,
#problem-checkboxes input {
  margin-right: 0.5rem;
}

#feedback-form textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: inherit;
  font-size: 0.9rem;
  resize: vertical;
  min-height: 100px;
}

#feedback-form textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.spinner {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

#message-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #2c3e50;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  max-width: 300px;
  word-wrap: break-word;
}

#user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

#user-email {
  color: #2c3e50;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  #auth-section {
    width: 100%;
    justify-content: center;
  }
  
  #login-form {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }
  
  #login-form input {
    width: 100%;
    min-width: unset;
  }
  
  main {
    padding: 0.5rem;
  }
  
  .case-card {
    padding: 1rem;
  }
  
  header h1 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  #message-overlay {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: unset;
  }
}