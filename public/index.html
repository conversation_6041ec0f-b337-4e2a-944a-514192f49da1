<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI阅卷反馈系统</title>
    <link rel="stylesheet" href="style.css" />
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  </head>
  <body>
    <header>
      <h1>AI 阅卷效果评测</h1>
      <div id="auth-section">
        <div id="user-info" style="display: none;">
          <span id="user-email"></span>
          <button id="logout-btn">登出</button>
        </div>
        <div id="login-form">
          <input type="email" id="email-input" placeholder="邮箱" required />
          <input
            type="password"
            id="password-input"
            placeholder="密码"
            required
          />
          <button id="login-btn">登录</button>
        </div>
      </div>
    </header>

    <main id="app-container" style="display: none;">
      <div class="case-card" id="context-card">
        <h3>评测原文</h3>
        <div id="context-content">
          <h4>学生答题图片:</h4>
          <img
            id="student-answer-img"
            src=""
            alt="学生答题图片"
            class="zoomable"
          />
          <h4>评分标准:</h4>
          <pre id="scoring-criteria"></pre>
        </div>
      </div>

      <div id="ai-results-container"></div>

      <div class="case-card" id="feedback-form-card">
        <h3>请您评判</h3>
        <form id="feedback-form">
          <p>1. 哪个方案的结果最好？</p>
          <div id="best-solution-options"></div>

          <p>2. AI 主要存在哪些问题？(可多选)</p>
          <div id="problem-checkboxes">
            <!-- Checkboxes will be inserted by JS -->
          </div>

          <p>3. 其他意见或修改建议:</p>
          <textarea id="comment-input" rows="4"></textarea>

          <button type="submit" id="submit-btn">提交反馈并加载下一题</button>
        </form>
      </div>
    </main>

    <div id="loading-spinner" class="spinner" style="display: none;"></div>
    <div id="message-overlay" style="display: none;"></div>

    <script type="module" src="script.js"></script>
  </body>
</html>