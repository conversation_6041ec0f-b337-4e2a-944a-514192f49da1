// Supabase配置 - 需要替换为实际的项目信息
const SUPABASE_URL = "https://YOUR_PROJECT_REF.supabase.co";
const SUPABASE_ANON_KEY = "YOUR_ANON_PUBLIC_KEY";

const { createClient } = supabase;
const sb = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

let currentCaseId = null;

// DOM Elements
const appContainer = document.getElementById("app-container");
const loginForm = document.getElementById("login-form");
const authSection = document.getElementById("auth-section");
const userInfo = document.getElementById("user-info");
const userEmailSpan = document.getElementById("user-email");
const logoutBtn = document.getElementById("logout-btn");
const loginBtn = document.getElementById("login-btn");
const feedbackForm = document.getElementById("feedback-form");

// --- AUTHENTICATION ---
sb.auth.onAuthStateChange(async (event, session) => {
  if (session) {
    // User is logged in
    loginForm.style.display = "none";
    userEmailSpan.textContent = session.user.email;
    userInfo.style.display = "block";
    appContainer.style.display = "block";
    await fetchCase();
  } else {
    // User is logged out
    loginForm.style.display = "block";
    userInfo.style.display = "none";
    appContainer.style.display = "none";
  }
});

loginBtn.addEventListener("click", async () => {
  const email = document.getElementById("email-input").value;
  const password = document.getElementById("password-input").value;
  
  if (!email || !password) {
    alert("请输入邮箱和密码");
    return;
  }
  
  toggleLoading(true);
  const { error } = await sb.auth.signInWithPassword({ email, password });
  toggleLoading(false);
  
  if (error) {
    alert(`登录失败: ${error.message}`);
  }
});

logoutBtn.addEventListener("click", async () => {
  await sb.auth.signOut();
});

// 支持回车键登录
document.getElementById("email-input").addEventListener("keypress", (e) => {
  if (e.key === "Enter") {
    loginBtn.click();
  }
});

document.getElementById("password-input").addEventListener("keypress", (e) => {
  if (e.key === "Enter") {
    loginBtn.click();
  }
});

// --- APP LOGIC ---
async function apiRequest(endpoint, options = {}) {
  const {
    data: { session },
  } = await sb.auth.getSession();
  
  if (!session) {
    alert("会话已过期，请刷新页面重新登录。");
    throw new Error("User not authenticated");
  }

  const defaultOptions = {
    headers: {
      Authorization: `Bearer ${session.access_token}`,
      "Content-Type": "application/json",
    },
  };

  const response = await fetch(endpoint, { ...defaultOptions, ...options });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API Error (${response.status}): ${errorText}`);
  }
  
  return response.json();
}

async function fetchCase() {
  toggleLoading(true);
  try {
    const data = await apiRequest("/api/get-case");
    if (data.message) {
      displayMessage(data.message);
      return;
    }
    renderCase(data);
  } catch (error) {
    displayMessage(`加载案例失败: ${error.message}`);
  } finally {
    toggleLoading(false);
  }
}

function renderCase(data) {
  currentCaseId = data.case_id;

  // Render context
  document.getElementById("student-answer-img").src = data.image_url;
  document.getElementById("scoring-criteria").textContent = data.criteria;

  // Render AI results
  const resultsContainer = document.getElementById("ai-results-container");
  resultsContainer.innerHTML = "";
  
  data.ai_results.forEach((result) => {
    const card = document.createElement("div");
    card.className = "case-card";
    card.innerHTML = `
      <h4>方案: ${escapeHtml(result.name)}</h4>
      <p><strong>识别文本:</strong></p>
      <pre>${escapeHtml(result.ocr_text || "N/A")}</pre>
      <p><strong>AI评分:</strong> ${escapeHtml(result.score)}</p>
      <p><strong>评分详情:</strong> ${escapeHtml(result.details)}</p>
    `;
    resultsContainer.appendChild(card);
  });

  // Render feedback form options
  const solutionOptions = document.getElementById("best-solution-options");
  solutionOptions.innerHTML = "";
  
  data.ai_results.forEach((result) => {
    const label = document.createElement("label");
    label.innerHTML = `
      <input type="radio" name="best_solution" value="${escapeHtml(result.id)}" required>
      ${escapeHtml(result.name)}
    `;
    solutionOptions.appendChild(label);
  });
  
  const noneLabel = document.createElement("label");
  noneLabel.innerHTML = `
    <input type="radio" name="best_solution" value="none">
    都不好
  `;
  solutionOptions.appendChild(noneLabel);

  // Render problem checkboxes
  const problems = [
    "识别/OCR错误",
    "评分标准理解错误",
    "事实性知识错误",
    "打分过松",
    "打分过严",
    "遗漏要点",
    "理由与分数不符",
    "理由不充分/牵强",
  ];
  
  const checkboxesContainer = document.getElementById("problem-checkboxes");
  checkboxesContainer.innerHTML = "";
  
  problems.forEach((problem) => {
    const label = document.createElement("label");
    label.innerHTML = `
      <input type="checkbox" name="problems" value="${escapeHtml(problem)}">
      ${escapeHtml(problem)}
    `;
    checkboxesContainer.appendChild(label);
  });

  // Reset form
  feedbackForm.reset();
  
  // Scroll to top
  window.scrollTo(0, 0);
}

feedbackForm.addEventListener("submit", async (e) => {
  e.preventDefault();
  
  const formData = new FormData(feedbackForm);
  const selectedProblems = Array.from(formData.getAll("problems"));
  const bestSolution = formData.get("best_solution");
  
  if (!bestSolution) {
    alert("请选择最佳方案");
    return;
  }

  const feedbackData = {
    case_id: currentCaseId,
    best_solution: bestSolution,
    problems: selectedProblems,
    comment: document.getElementById("comment-input").value.trim(),
  };

  toggleLoading(true);
  
  try {
    await apiRequest("/api/submit-feedback", {
      method: "POST",
      body: JSON.stringify(feedbackData),
    });
    
    displayMessage("反馈提交成功！正在加载下一题...");
    await fetchCase(); // Load next case
  } catch (error) {
    displayMessage(`提交失败: ${error.message}`);
  } finally {
    toggleLoading(false);
  }
});

// --- UI HELPERS ---
function toggleLoading(isLoading) {
  const spinner = document.getElementById("loading-spinner");
  const submitBtn = document.getElementById("submit-btn");
  const loginButton = document.getElementById("login-btn");
  
  spinner.style.display = isLoading ? "block" : "none";
  
  if (submitBtn) {
    submitBtn.disabled = isLoading;
    submitBtn.textContent = isLoading ? "提交中..." : "提交反馈并加载下一题";
  }
  
  if (loginButton) {
    loginButton.disabled = isLoading;
    loginButton.textContent = isLoading ? "登录中..." : "登录";
  }
}

function displayMessage(msg) {
  const overlay = document.getElementById("message-overlay");
  overlay.textContent = msg;
  overlay.style.display = "block";
  
  setTimeout(() => {
    overlay.style.display = "none";
  }, 4000);
}

function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// 图片点击放大功能
document.addEventListener('click', (e) => {
  if (e.target.classList.contains('zoomable')) {
    const img = e.target;
    if (img.style.transform === 'scale(2)') {
      img.style.transform = 'scale(1)';
      img.style.cursor = 'zoom-in';
      img.style.zIndex = 'auto';
      img.style.position = 'relative';
    } else {
      img.style.transform = 'scale(2)';
      img.style.cursor = 'zoom-out';
      img.style.zIndex = '999';
      img.style.position = 'relative';
    }
  }
});

// 初始化检查认证状态
sb.auth.getSession().then(({ data: { session } }) => {
  if (session) {
    loginForm.style.display = "none";
    userEmailSpan.textContent = session.user.email;
    userInfo.style.display = "block";
    appContainer.style.display = "block";
    fetchCase();
  }
});