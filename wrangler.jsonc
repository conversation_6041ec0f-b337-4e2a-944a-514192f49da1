/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "ai-feedback-worker",
	"main": "src/index.ts",
	"compatibility_date": "2024-11-11",
	"observability": {
		"enabled": true
	},

	// 绑定 D1 数据库
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "ai-feedback-db",
			"database_id": "YOUR_D1_DATABASE_ID"
		}
	],

	// 绑定 R2 存储桶
	"r2_buckets": [
		{
			"binding": "RESULTS_BUCKET",
			"bucket_name": "ai-feedback-results"
		}
	],

	// 绑定环境变量 (用于Worker)
	"vars": {
		"SUPABASE_JWT_SECRET": "YOUR_SUPABASE_JWT_SECRET",
		"R2_PUBLIC_URL": "https://pub-xxxx.r2.dev"
	}
}
