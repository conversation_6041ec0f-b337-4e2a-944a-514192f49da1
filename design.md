# AI 阅卷反馈系统 (Cloudflare + Supabase)

## 一、项目概览与架构

**目标：** 构建一个安全、高效、移动优先的网页，供已认证用户评测和对比不同 AI 模型的阅卷结果，并将反馈数据用于模型和提示词的迭代。

**技术栈：**

- **前端:** Cloudflare Pages (HTML, CSS, Vanilla JavaScript)
- **后端:** Cloudflare Workers
- **数据库:** Cloudflare D1
- **文件存储:** Cloudflare R2
- **用户认证:** Supabase Auth

**数据流：**

1. **准备:** `promptfoo`测试结果(`results.json`)和答题图片上传至 **R2**。
2. **认证:** 用户在 **Pages** 网页上通过 **Supabase** 登录，获取 JWT。
3. **请求:** 前端携带 JWT 向 **Worker** 发起 API 请求。
4. **验证:** **Worker** 使用 Supabase 的 JWT Secret 验证 JWT，并解析出用户信息。
5. **处理:** **Worker** 从 **R2** 读取测试案例，从 **D1** 查询该用户的评测历史，返回一个未评测的案例。
6. **反馈:** 用户提交反馈，**Worker** 将带有`user_id`的反馈数据存入 **D1**。
7. **分析:** 管理员查询 **D1** 数据库，分析各模型表现和用户反馈，为后续奖励和模型优化提供依据。

---

## 二、准备工作：平台设置

### 1. Cloudflare 设置

1. **创建 R2 存储桶:**
   - 进入 Cloudflare Dashboard -> R2。
   - 创建一个存储桶，例如 `ai-feedback-results`。
   - **重要:** 进入该存储桶的 "Settings" 页面，允许公开访问（或者为每个图片生成签名 URL，前者更简单）。记下公开访问的域名，例如 `https://pub-xxxx.r2.dev`。
   - 上传你的 `results.json` 文件和所有学生答题图片。
2. **创建 D1 数据库:**

   - 进入 Cloudflare Dashboard -> Workers & Pages -> D1。
   - 创建一个数据库，例如 `ai-feedback-db`。
   - 在该数据库的控制台执行以下 SQL，创建表格：

   ```sql
   CREATE TABLE Feedbacks (
     id INTEGER PRIMARY KEY AUTOINCREMENT,
     case_id TEXT NOT NULL,
     user_id TEXT NOT NULL,
     user_email TEXT,
     best_solution TEXT NOT NULL,
     problems TEXT,
     comment TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );

   CREATE UNIQUE INDEX idx_case_user ON Feedbacks (case_id, user_id);
   ```

### 2. Supabase 设置

1. **创建 Supabase 项目。**
2. **获取 API 信息:**
   - 前往 "Project Settings" -> "API"。
   - 复制 **Project URL** 和 **`anon` public key**。
3. **获取 JWT Secret:**
   - 前往 "Project Settings" -> "API" -> "JWT Settings"。
   - 复制 **JWT Secret**。这是一个长字符串，**严禁暴露在前端**。
4. **配置认证方式:**
   - 前往 "Authentication" -> "Providers"。
   - 启用并配置你希望支持的登录方式，例如 "Email"。确保关闭“需要邮件确认”选项以简化测试流程。

---

## 三、项目文件结构

```text
ai-feedback-system/
├── public/                  # 前端代码 (部署到 Cloudflare Pages)
│   ├── index.html
│   ├── style.css
│   └── script.js
├── src/                     # 后端代码 (部署到 Cloudflare Workers)
│   └── index.js
├── package.json
└── wrangler.toml            # Cloudflare 配置
```

---

## 四、代码实现

### 1. `package.json`

```json
{
  "name": "ai-feedback-system",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "start": "wrangler pages dev public --compatibility-date=2023-11-21 --port=8788",
    "deploy-worker": "wrangler deploy src/index.js",
    "deploy-pages": "wrangler pages deploy public"
  },
  "dependencies": {
    "@supabase/supabase-js": "^2.38.4",
    "@tsndr/cloudflare-worker-jwt": "^2.3.0"
  },
  "devDependencies": {
    "wrangler": "^3.17.1"
  }
}
```

_运行 `npm install` 安装依赖。_

### 2. `wrangler.toml` (项目根目录)

```toml
name = "ai-feedback-worker"
main = "src/index.js"
compatibility_date = "2023-11-21"

# 绑定 D1 数据库
[[d1_databases]]
binding = "DB"
database_name = "ai-feedback-db"
database_id = "YOUR_D1_DATABASE_ID" # 从Cloudflare D1控制台获取

# 绑定 R2 存储桶
[[r2_buckets]]
binding = "RESULTS_BUCKET"
bucket_name = "ai-feedback-results"

# 绑定环境变量 (用于Worker)
[vars]
SUPABASE_JWT_SECRET = "YOUR_SUPABASE_JWT_SECRET" # 粘贴你的JWT Secret
R2_PUBLIC_URL = "https://pub-xxxx.r2.dev" # 粘贴你的R2公开访问URL
```

### 3. 前端代码 (`public/` 目录下)

**`public/index.html`**

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI阅卷反馈系统</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <header>
      <h1>AI 阅卷效果评测</h1>
      <div id="auth-section">
        <div id="user-info" style="display: none;">
          <span id="user-email"></span>
          <button id="logout-btn">登出</button>
        </div>
        <div id="login-form">
          <input type="email" id="email-input" placeholder="邮箱" required />
          <input
            type="password"
            id="password-input"
            placeholder="密码"
            required
          />
          <button id="login-btn">登录</button>
        </div>
      </div>
    </header>

    <main id="app-container" style="display: none;">
      <div class="case-card" id="context-card">
        <h3>评测原文</h3>
        <div id="context-content">
          <h4>学生答题图片:</h4>
          <img
            id="student-answer-img"
            src=""
            alt="学生答题图片"
            class="zoomable"
          />
          <h4>评分标准:</h4>
          <pre id="scoring-criteria"></pre>
        </div>
      </div>

      <div id="ai-results-container"></div>

      <div class="case-card" id="feedback-form-card">
        <h3>请您评判</h3>
        <form id="feedback-form">
          <p>1. 哪个方案的结果最好？</p>
          <div id="best-solution-options"></div>

          <p>2. AI 主要存在哪些问题？(可多选)</p>
          <div id="problem-checkboxes">
            <!-- Checkboxes will be inserted by JS -->
          </div>

          <p>3. 其他意见或修改建议:</p>
          <textarea id="comment-input" rows="4"></textarea>

          <button type="submit" id="submit-btn">提交反馈并加载下一题</button>
        </form>
      </div>
    </main>

    <div id="loading-spinner" class="spinner" style="display: none;"></div>
    <div id="message-overlay" style="display: none;"></div>

    <script type="module" src="script.js"></script>
  </body>
</html>
```

**`public/style.css`** (精简版)

```css
body {
  font-family: sans-serif;
  margin: 0;
  background-color: #f4f4f9;
  color: #333;
}
header {
  background: #fff;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
main {
  padding: 1rem;
}
.case-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 1rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
#login-form input {
  margin-right: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}
button {
  padding: 0.6rem 1.2rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
button:hover {
  background-color: #0056b3;
}
#logout-btn {
  background-color: #dc3545;
}
img.zoomable {
  max-width: 100%;
  cursor: zoom-in;
}
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #eee;
  padding: 0.5rem;
  border-radius: 4px;
}
#feedback-form-card p {
  font-weight: bold;
}
#feedback-form textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.spinner {
  /* ...样式... */
}
#message-overlay {
  /* ...样式... */
}
```

**`public/script.js`**

```javascript
// Import Supabase client library via a CDN in the HTML, or use a bundler.
// For simplicity, let's assume you've added this to your HTML <head>:
// <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
const { createClient } = supabase;

const SUPABASE_URL = "https://YOUR_PROJECT_REF.supabase.co";
const SUPABASE_ANON_KEY = "YOUR_ANON_PUBLIC_KEY";

const sb = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

let currentCaseId = null;

// DOM Elements
const appContainer = document.getElementById("app-container");
const loginForm = document.getElementById("login-form");
const authSection = document.getElementById("auth-section");
const userInfo = document.getElementById("user-info");
const userEmailSpan = document.getElementById("user-email");
const logoutBtn = document.getElementById("logout-btn");
const loginBtn = document.getElementById("login-btn");
const feedbackForm = document.getElementById("feedback-form");

// --- AUTHENTICATION ---
sb.auth.onAuthStateChange(async (event, session) => {
  if (session) {
    // User is logged in
    loginForm.style.display = "none";
    userEmailSpan.textContent = session.user.email;
    userInfo.style.display = "block";
    appContainer.style.display = "block";
    await fetchCase();
  } else {
    // User is logged out
    loginForm.style.display = "block";
    userInfo.style.display = "none";
    appContainer.style.display = "none";
  }
});

loginBtn.addEventListener("click", async () => {
  const email = document.getElementById("email-input").value;
  const password = document.getElementById("password-input").value;
  const { error } = await sb.auth.signInWithPassword({ email, password });
  if (error) alert(`登录失败: ${error.message}`);
});

logoutBtn.addEventListener("click", async () => {
  await sb.auth.signOut();
});

// --- APP LOGIC ---
async function apiRequest(endpoint, options = {}) {
  const {
    data: { session },
  } = await sb.auth.getSession();
  if (!session) {
    alert("会话已过期，请刷新页面重新登录。");
    throw new Error("User not authenticated");
  }

  const defaultOptions = {
    headers: {
      Authorization: `Bearer ${session.access_token}`,
      "Content-Type": "application/json",
    },
  };

  const response = await fetch(endpoint, { ...defaultOptions, ...options });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API Error (${response.status}): ${errorText}`);
  }
  return response.json();
}

async function fetchCase() {
  toggleLoading(true);
  try {
    const data = await apiRequest("/api/get-case");
    if (data.message) {
      displayMessage(data.message);
      return;
    }
    renderCase(data);
  } catch (error) {
    displayMessage(error.message);
  } finally {
    toggleLoading(false);
  }
}

function renderCase(data) {
  currentCaseId = data.case_id;

  // Render context
  document.getElementById("student-answer-img").src = data.image_url;
  document.getElementById("scoring-criteria").textContent = data.criteria;

  // Render AI results
  const resultsContainer = document.getElementById("ai-results-container");
  resultsContainer.innerHTML = "";
  data.ai_results.forEach((result) => {
    const card = document.createElement("div");
    card.className = "case-card";
    card.innerHTML = `
            <h4>方案: ${result.name}</h4>
            <p><strong>识别文本:</strong></p>
            <pre>${result.ocr_text || "N/A"}</pre>
            <p><strong>AI评分:</strong> ${result.score}</p>
            <p><strong>评分详情:</strong> ${result.details}</p>
        `;
    resultsContainer.appendChild(card);
  });

  // Render feedback form options
  const solutionOptions = document.getElementById("best-solution-options");
  solutionOptions.innerHTML = "";
  data.ai_results.forEach((result) => {
    solutionOptions.innerHTML += `
            <label>
                <input type="radio" name="best_solution" value="${result.id}" required>
                ${result.name}
            </label><br>
        `;
  });
  solutionOptions.innerHTML += `
        <label>
            <input type="radio" name="best_solution" value="none">
            都不好
        </label>
    `;

  const problems = [
    "识别/OCR错误",
    "评分标准理解错误",
    "事实性知识错误",
    "打分过松",
    "打分过严",
    "遗漏要点",
    "理由与分数不符",
    "理由不充分/牵强",
  ];
  const checkboxesContainer = document.getElementById("problem-checkboxes");
  checkboxesContainer.innerHTML = "";
  problems.forEach((p) => {
    checkboxesContainer.innerHTML += `
            <label>
                <input type="checkbox" name="problems" value="${p}">
                ${p}
            </label><br>
        `;
  });

  feedbackForm.reset();
}

feedbackForm.addEventListener("submit", async (e) => {
  e.preventDefault();
  toggleLoading(true);

  const formData = new FormData(feedbackForm);
  const selectedProblems = Array.from(formData.getAll("problems"));

  const feedbackData = {
    case_id: currentCaseId,
    best_solution: formData.get("best_solution"),
    problems: selectedProblems,
    comment: document.getElementById("comment-input").value,
  };

  try {
    await apiRequest("/api/submit-feedback", {
      method: "POST",
      body: JSON.stringify(feedbackData),
    });
    await fetchCase(); // Load next case
  } catch (error) {
    displayMessage(`提交失败: ${error.message}`);
  } finally {
    toggleLoading(false);
  }
});

// --- UI HELPERS ---
function toggleLoading(isLoading) {
  document.getElementById("loading-spinner").style.display = isLoading
    ? "block"
    : "none";
}

function displayMessage(msg) {
  const overlay = document.getElementById("message-overlay");
  overlay.textContent = msg;
  overlay.style.display = "block";
  setTimeout(() => {
    overlay.style.display = "none";
  }, 4000);
}
```

#### 4. 后端代码 (`src/index.js`)

```javascript
import jwt from "@tsndr/cloudflare-worker-jwt";

// Authentication middleware
async function requireAuth(request, env) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return {
      error: new Response("Missing Authorization header", { status: 401 }),
    };
  }
  const token = authHeader.substring(7);
  const isValid = await jwt.verify(token, env.SUPABASE_JWT_SECRET);
  if (!isValid) {
    return { error: new Response("Invalid token", { status: 401 }) };
  }
  return { user: jwt.decode(token).payload };
}

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    if (url.pathname.startsWith("/api/")) {
      const authResult = await requireAuth(request, env);
      if (authResult.error) return authResult.error;
      request.user = authResult.user;
    }

    if (url.pathname === "/api/get-case" && request.method === "GET") {
      try {
        const resultsObject = await env.RESULTS_BUCKET.get("results.json");
        if (!resultsObject) throw new Error("results.json not found in R2.");
        const promptfooData = await resultsObject.json();
        const allCases = promptfooData.results.tests;

        const userId = request.user.sub;
        const { results: reviewedCases } = await env.DB.prepare(
          "SELECT case_id FROM Feedbacks WHERE user_id = ?"
        )
          .bind(userId)
          .all();
        const reviewedCaseIds = new Set(reviewedCases.map((r) => r.case_id));

        const nextCase = allCases.find(
          (c) =>
            c.vars && c.vars.case_id && !reviewedCaseIds.has(c.vars.case_id)
        );

        if (!nextCase) {
          return new Response(
            JSON.stringify({ message: "太棒了！您已完成所有评测任务！" }),
            {
              headers: { "Content-Type": "application/json" },
            }
          );
        }

        const responsePayload = {
          case_id: nextCase.vars.case_id,
          image_url: `${env.R2_PUBLIC_URL}/${nextCase.vars.image_file}`,
          criteria: nextCase.vars.criteria,
          ai_results: nextCase.prompts.map((prompt, index) => {
            const output = nextCase.outputs[index] || {};
            const provider = prompt.provider || `workflow_${index + 1}`;
            return {
              id: provider,
              name: prompt.label || provider, // Use 'label' from promptfoo config if available
              ocr_text: output.vars ? output.vars.ocr_text : "N/A",
              score: output.vars ? output.vars.score : "N/A",
              details: output.value,
            };
          }),
        };

        return new Response(JSON.stringify(responsePayload), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (e) {
        return new Response(e.message, { status: 500 });
      }
    }

    if (url.pathname === "/api/submit-feedback" && request.method === "POST") {
      try {
        const feedback = await request.json();
        const { sub: userId, email: userEmail } = request.user;

        await env.DB.prepare(
          "INSERT INTO Feedbacks (case_id, user_id, user_email, best_solution, problems, comment) VALUES (?, ?, ?, ?, ?, ?)"
        )
          .bind(
            feedback.case_id,
            userId,
            userEmail,
            feedback.best_solution,
            JSON.stringify(feedback.problems || []),
            feedback.comment
          )
          .run();

        return new Response(JSON.stringify({ success: true }), { status: 201 });
      } catch (e) {
        // Handle unique constraint error (user already submitted for this case)
        if (e.message.includes("UNIQUE constraint failed")) {
          return new Response(
            "You have already submitted feedback for this case.",
            { status: 409 }
          );
        }
        return new Response(e.message, { status: 500 });
      }
    }

    return new Response("Not Found", { status: 404 });
  },
};
```

---

## 五、部署与运行

1. **本地测试:**

   - 在 `wrangler.toml` 的同级目录下创建 `.dev.vars` 文件并填入机密信息：

   ```env
   SUPABASE_JWT_SECRET="YOUR_SUPABASE_JWT_SECRET"
   R2_PUBLIC_URL="https://pub-xxxx.r2.dev"
   ```

   - 运行 `npm start`。
   - 在浏览器中打开 `http://localhost:8788` 进行测试。

2. **正式部署:**
   - **部署 Worker:** `npm run deploy-worker`
   - **部署 Pages:** `npm run deploy-pages`
   - **配置环境变量:** 前往 Cloudflare Dashboard -> Workers & Pages -> 你的 Worker -> Settings -> Variables，添加 `SUPABASE_JWT_SECRET` 和 `R2_PUBLIC_URL` 这两个环境变量。
   - 现在，你的应用就可以通过 Cloudflare Pages 提供的域名公开访问了。

这个完整方案为你提供了一个功能齐全、安全可靠且可扩展的 AI 反馈系统。
