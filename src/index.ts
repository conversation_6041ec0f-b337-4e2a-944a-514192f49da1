import jwt from '@tsndr/cloudflare-worker-jwt';

// 类型定义
interface PromptfooResult {
  id: string;
  prompt: string;
  vars: Record<string, any>;
  response: {
    output: string;
    tokenUsage?: {
      total: number;
      prompt: number;
      completion: number;
    };
  };
  success: boolean;
  score: number;
  namedScores: Record<string, number>;
  gradingResult?: {
    pass: boolean;
    score: number;
    reason: string;
  };
}

interface PromptfooData {
  results: PromptfooResult[];
  stats: {
    successes: number;
    failures: number;
    tokenUsage: {
      total: number;
      prompt: number;
      completion: number;
    };
  };
}

interface FeedbackData {
  caseId: string;
  bestOption: string;
  issues: string[];
  suggestions: string;
}

interface AuthResult {
	error?: Response;
	user?: any;
}

// 获取案例接口
async function getCase(env: Env): Promise<Response> {
  try {
    // 从 R2 获取 results.json
    const resultsObject = await env.RESULTS_BUCKET.get('results.json');
    if (!resultsObject) {
      return new Response(JSON.stringify({ error: 'Results file not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const resultsText = await resultsObject.text();
    const promptfooData: PromptfooData = JSON.parse(resultsText);

    // 从数据库获取已评测的案例ID
    const evaluatedCases = await env.DB.prepare(
      'SELECT case_id FROM feedback'
    ).all();

    const evaluatedIds = new Set(evaluatedCases.results.map((row: any) => row.case_id));

    // 找到第一个未评测的案例
    const unevaluatedCase = promptfooData.results?.find((result: PromptfooResult) => 
      !evaluatedIds.has(result.id)
    );

    if (!unevaluatedCase) {
      return new Response(JSON.stringify({ message: 'All cases have been evaluated' }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify(unevaluatedCase), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting case:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// 提交反馈接口
async function submitFeedback(request: Request, env: Env): Promise<Response> {
  try {
    const feedback: FeedbackData = await request.json();

    // 验证必需字段
    if (!feedback.caseId || !feedback.bestOption) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 插入反馈到数据库
    await env.DB.prepare(`
      INSERT INTO feedback (case_id, best_option, issues, suggestions, created_at)
      VALUES (?, ?, ?, ?, datetime('now'))
    `).bind(
      feedback.caseId,
      feedback.bestOption,
      JSON.stringify(feedback.issues || []),
      feedback.suggestions || ''
    ).run();

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Authentication middleware
async function requireAuth(request: Request, env: Env): Promise<AuthResult> {
	const authHeader = request.headers.get('Authorization');
	if (!authHeader || !authHeader.startsWith('Bearer ')) {
		return {
			error: new Response('Missing Authorization header', { status: 401 }),
		};
	}
	const token = authHeader.substring(7);
	const isValid = await jwt.verify(token, env.SUPABASE_JWT_SECRET);
	if (!isValid) {
		return { error: new Response('Invalid token', { status: 401 }) };
	}
	return { user: jwt.decode(token).payload };
}

export default {
	async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
		const url = new URL(request.url);

		// CORS headers
		const corsHeaders = {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization',
		};

		// Handle CORS preflight requests
		if (request.method === 'OPTIONS') {
			return new Response(null, {
				status: 200,
				headers: corsHeaders,
			});
		}

		// Authentication for API routes
		if (url.pathname.startsWith('/api/')) {
			const authResult = await requireAuth(request, env);
			if (authResult.error) {
				return new Response(authResult.error.body, {
					status: authResult.error.status,
					headers: corsHeaders,
				});
			}
			(request as any).user = authResult.user;
		}

		// API: Get next case for evaluation
		if (url.pathname === '/api/get-case' && request.method === 'GET') {
			try {
				const resultsObject = await env.RESULTS_BUCKET.get('results.json');
				if (!resultsObject) {
					throw new Error('results.json not found in R2.');
				}
				const promptfooData = await resultsObject.json();
				const allCases = promptfooData.results.tests;

				const userId = (request as any).user.sub;
				const { results: reviewedCases } = await env.DB.prepare(
					'SELECT case_id FROM Feedbacks WHERE user_id = ?'
				)
					.bind(userId)
					.all();
				const reviewedCaseIds = new Set(reviewedCases.map((r: any) => r.case_id));

				const nextCase = allCases.find(
					(c: any) =>
						c.vars && c.vars.case_id && !reviewedCaseIds.has(c.vars.case_id)
				);

				if (!nextCase) {
					return new Response(
						JSON.stringify({ message: '太棒了！您已完成所有评测任务！' }),
						{
							headers: { 'Content-Type': 'application/json', ...corsHeaders },
						}
					);
				}

				const responsePayload = {
					case_id: nextCase.vars.case_id,
					image_url: `${env.R2_PUBLIC_URL}/${nextCase.vars.image_file}`,
					criteria: nextCase.vars.criteria,
					ai_results: nextCase.prompts.map((prompt: any, index: number) => {
						const output = nextCase.outputs[index] || {};
						const provider = prompt.provider || `workflow_${index + 1}`;
						return {
							id: provider,
							name: prompt.label || provider,
							ocr_text: output.vars ? output.vars.ocr_text : 'N/A',
							score: output.vars ? output.vars.score : 'N/A',
							details: output.value,
						};
					}),
				};

				return new Response(JSON.stringify(responsePayload), {
					headers: { 'Content-Type': 'application/json', ...corsHeaders },
				});
			} catch (e: any) {
				return new Response(e.message, { 
					status: 500,
					headers: corsHeaders,
				});
			}
		}

		// API: Submit feedback
		if (url.pathname === '/api/submit-feedback' && request.method === 'POST') {
			try {
				const feedback = await request.json();
				const { sub: userId, email: userEmail } = (request as any).user;

				await env.DB.prepare(
					'INSERT INTO Feedbacks (case_id, user_id, user_email, best_solution, problems, comment) VALUES (?, ?, ?, ?, ?, ?)'
				)
					.bind(
						feedback.case_id,
						userId,
						userEmail,
						feedback.best_solution,
						JSON.stringify(feedback.problems || []),
						feedback.comment
					)
					.run();

				return new Response(JSON.stringify({ success: true }), { 
					status: 201,
					headers: { 'Content-Type': 'application/json', ...corsHeaders },
				});
			} catch (e: any) {
				// Handle unique constraint error (user already submitted for this case)
				if (e.message.includes('UNIQUE constraint failed')) {
					return new Response(
						'You have already submitted feedback for this case.',
						{ 
							status: 409,
							headers: corsHeaders,
						}
					);
				}
				return new Response(e.message, { 
					status: 500,
					headers: corsHeaders,
				});
			}
		}

		return new Response('Not Found', { 
			status: 404,
			headers: corsHeaders,
		});
	},
} satisfies ExportedHandler<Env>;
